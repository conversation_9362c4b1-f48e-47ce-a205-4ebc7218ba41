# 修复文件拒绝全部时的错误

## 问题描述

在使用 Continue IntelliJ 插件时，当修改文件并选择"拒绝全部"时，特别是在改动量很大的情况下，会出现以下错误：

```
java.lang.Throwable: Assertion failed
	at com.intellij.openapi.diagnostic.Logger.assertTrue(Logger.java:472)
	at com.intellij.openapi.command.impl.UndoManagerImpl.undo(UndoManagerImpl.java:465)
	at com.github.continuedev.continueintellijextension.editor.DiffStreamHandler.undoChanges$lambda$9(DiffStreamHandler.kt:294)
```

## 问题根源

1. **UndoManager 批量操作问题**：原始代码尝试通过计算变更数量来执行多次 undo 操作，但 IntelliJ 的 undo 系统在处理大量变更时可能出现状态不一致。

2. **缺乏错误处理**：原始代码没有充分的错误处理机制，当 undo 操作失败时没有备选方案。

3. **状态检查不足**：没有检查 UndoManager 和 FileEditor 的有效性。

## 解决方案

### 1. 改进 `undoChanges()` 方法

- 添加了完整的错误处理机制
- 实现了逐个 undo 而不是批量 undo
- 添加了 `revertChangesDirectly()` 作为备选方案
- 增加了状态检查和空值检查

### 2. 改进 `rejectAll()` 方法

- 添加了 try-catch 错误处理
- 确保即使出错也能正确关闭状态

### 3. 改进 `VerticalDiffBlock.revertDiff()` 方法

- 添加了边界检查，确保行号在有效范围内
- 增加了文档长度检查
- 添加了详细的错误处理

### 4. 改进 `VerticalDiffBlock.handleReject()` 方法

- 添加了完整的错误处理
- 确保回调函数始终被调用以维护状态一致性

## 主要改进点

1. **多层错误处理**：
   - UndoManager 操作失败时，自动切换到直接撤销方法
   - 直接撤销失败时，提供用户通知机制

2. **状态安全性**：
   - 检查文件编辑器的有效性
   - 验证行号和偏移量的边界
   - 确保文档操作的安全性

3. **渐进式撤销**：
   - 逐个执行 undo 操作而不是批量执行
   - 按相反顺序处理 diff blocks 以避免行号偏移问题

4. **详细日志**：
   - 添加了详细的错误日志以便调试
   - 记录失败的具体原因和位置

## 测试建议

1. 测试大量变更的文件拒绝操作
2. 测试部分接受后的拒绝全部操作
3. 测试在文件编辑器状态异常时的处理
4. 验证错误恢复机制的有效性

这些修改应该能够显著减少"拒绝全部"操作时的错误，并提供更好的用户体验。
